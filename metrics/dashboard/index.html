<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bayaan Project Metrics Dashboard</title>
  <link rel="stylesheet" href="style.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
  <div class="dashboard-container">
    <!-- Header -->
    <header class="dashboard-header">
      <div class="header-content">
        <h1><i class="fas fa-chart-line"></i> Bayaan Project Metrics</h1>
        <div class="controls">
          <label for="reportSelect"><i class="fas fa-calendar-alt"></i> Report:</label>
          <select id="reportSelect"></select>
          <button id="refreshBtn" class="refresh-btn"><i class="fas fa-sync-alt"></i></button>
        </div>
      </div>
    </header>

    <!-- Main Dashboard Content -->
    <main class="dashboard-main">
      <!-- Overview Cards -->
      <section class="overview-section">
        <div class="overview-cards" id="overviewCards">
          <!-- Overview cards will be generated here -->
        </div>
      </section>

      <!-- Charts Section -->
      <section class="charts-section">
        <div class="chart-grid">
          <div class="chart-card">
            <h3><i class="fas fa-file-code"></i> Code Distribution by Language</h3>
            <canvas id="languageChart"></canvas>
          </div>
          <div class="chart-card">
            <h3><i class="fas fa-folder"></i> File Count by Type</h3>
            <canvas id="fileCountChart"></canvas>
          </div>
          <div class="chart-card">
            <h3><i class="fas fa-code"></i> Lines of Code Distribution</h3>
            <canvas id="locChart"></canvas>
          </div>
          <div class="chart-card">
            <h3><i class="fas fa-mobile-alt"></i> Build Performance</h3>
            <canvas id="buildChart"></canvas>
          </div>
        </div>
      </section>

      <!-- Detailed Metrics -->
      <section class="details-section">
        <div class="details-grid">
          <!-- Code Quality Card -->
          <div class="detail-card">
            <h3><i class="fas fa-bug"></i> Code Quality</h3>
            <div id="codeQuality"></div>
          </div>

          <!-- Asset Analysis Card -->
          <div class="detail-card">
            <h3><i class="fas fa-images"></i> Asset Analysis</h3>
            <div id="assetAnalysis"></div>
          </div>

          <!-- Build Results Card -->
          <div class="detail-card">
            <h3><i class="fas fa-cogs"></i> Build Results</h3>
            <div id="buildResults"></div>
          </div>

          <!-- Project Info Card -->
          <div class="detail-card">
            <h3><i class="fas fa-info-circle"></i> Project Information</h3>
            <div id="projectInfo"></div>
          </div>
        </div>
      </section>
    </main>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>Loading metrics...</p>
      </div>
    </div>
  </div>

  <script src="app.js"></script>
</body>
</html>
