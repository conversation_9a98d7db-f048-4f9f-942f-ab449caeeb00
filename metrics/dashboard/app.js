// Dashboard logic for metrics reports
const reportsDir = '../reports/';
const reportSelect = document.getElementById('reportSelect');
const reportContent = document.getElementById('reportContent');

async function fetchReportList() {
  // List of reports is hardcoded for static hosting; update if new reports are added
  // You can automate this with a script if needed
  return [
    'dev-report-20250901-052741.json',
    'dev-report-20250906-234657.json',
  ];
}

async function fetchReport(filename) {
  const res = await fetch(reportsDir + filename);
  if (!res.ok) throw new Error('Failed to load report: ' + filename);
  return res.json();
}



// Map report keys to human-friendly names
const FRIENDLY_NAMES = {
  // Top-level
  metadata: 'Report Info',
  file_count: 'File Count',
  lines_of_code: 'Lines of Code',
  assets: 'Asset Analysis',
  dart_analysis: 'Dart Analysis',
  builds: 'Build Results',
  collector_outputs: 'Collector Outputs',
  // Common inner keys
  timestamp: 'Timestamp',
  project_name: 'Project Name',
  report_version: 'Report Version',
  collection_duration: 'Collection Duration',
  total: 'Total',
  private: 'Private',
  public: 'Public',
  private_top10: 'Top 10 Private Assets',
  public_top10: 'Top 10 Public Assets',
  note: 'Note',
  errors: 'Errors',
  warnings: 'Warnings',
  info: 'Info',
  private_app: 'Private App',
  public_app: 'Public App',
  android: 'Android',
  ios: 'iOS',
  debug: 'Debug',
  release: 'Release',
  build_time_seconds: 'Build Time (seconds)',
  build_successful: 'Build Successful',
  output_size_bytes: 'Output Size (bytes)',
  file_count_available: 'File Count Available',
  loc_available: 'Lines of Code Available',
  assets_available: 'Assets Available',
  dart_analysis_available: 'Dart Analysis Available',
  build_analysis_available: 'Build Analysis Available',
};


function renderAssetTable(title, assets) {
  if (!Array.isArray(assets) || assets.length === 0) return '';
  let html = `<h4>${title}</h4><table class="metrics-table"><thead><tr><th>Asset</th><th>Size (bytes)</th></tr></thead><tbody>`;
  assets.forEach(asset => {
    if (typeof asset === 'object' && asset !== null) {
      const name = asset.name || asset.path || asset.asset || 'Unknown';
      const size = asset.size || asset.size_bytes || asset.bytes || 'N/A';
      html += `<tr><td>${name}</td><td>${size}</td></tr>`;
    }
  });
  html += '</tbody></table>';
  return html;
}



function formatSizeMB(bytes) {
  if (!bytes || isNaN(bytes) || bytes === 0) return '0 MB';
  return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
}



function formatBuildTime(seconds) {
  if (!seconds || isNaN(seconds) || seconds === 0) return '0';
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  if (mins > 0) {
    return `${mins}m${secs > 0 ? ' ' + secs + 's' : ''} (${seconds}s)`;
  } else {
    return `${secs}s (${seconds}s)`;
  }
}

function renderBuildsTable(builds) {
  // builds: { private_app: {android: {...}, ios: {...}}, public_app: {...} }
  const rows = [];
  const appTypes = ['private_app', 'public_app'];
  const platforms = ['android', 'ios'];
  const buildTypes = ['debug', 'release'];
  appTypes.forEach(app => {
    if (!builds[app]) return;
    platforms.forEach(platform => {
      if (!builds[app][platform]) return;
      buildTypes.forEach(buildType => {
        const data = builds[app][platform][buildType];
        if (!data || !data.build_successful) return; // Only show successful builds
        rows.push({
          app: FRIENDLY_NAMES[app] || app,
          platform: FRIENDLY_NAMES[platform] || platform,
          buildType: buildType,
          buildTime: formatBuildTime(data.build_time_seconds),
          outputSize: formatSizeMB(data.output_size_bytes)
        });
      });
    });
  });
  let html = '<table class="metrics-table"><thead><tr><th>App</th><th>Platform</th><th>Build Type</th><th>Build Time (s)</th><th>Output Size</th></tr></thead><tbody>';
  rows.forEach(row => {
    // Add chip for build type
    const chip = `<span style="display:inline-block;padding:2px 8px;border-radius:12px;font-size:0.9em;background:${row.buildType==='release'?'#d1fae5':'#e0e7ff'};color:${row.buildType==='release'?'#065f46':'#3730a3'};margin-left:6px;">${row.buildType.charAt(0).toUpperCase() + row.buildType.slice(1)}</span>`;
    html += `<tr><td>${row.app}</td><td>${row.platform}</td><td>${chip}</td><td>${row.buildTime}</td><td>${row.outputSize}</td></tr>`;
  });
  html += '</tbody></table>';
  return html;
}

function renderReport(report, filename) {
  reportContent.innerHTML = '';
  const title = document.createElement('h2');
  title.textContent = formatReportDate(filename);
  reportContent.appendChild(title);

  // Show top-level keys as cards or tables
  Object.entries(report).forEach(([key, value]) => {
    const card = document.createElement('div');
    card.className = 'metric-card';
    const heading = document.createElement('h3');
    heading.textContent = FRIENDLY_NAMES[key] || key;
    card.appendChild(heading);

    // Special rendering for asset analysis
    if (key === 'assets' && typeof value === 'object' && value !== null) {
      let html = '';
      if (Array.isArray(value.private_top10) && value.private_top10.length > 0) {
        html += renderAssetTable('Top 10 Private Assets', value.private_top10);
      }
      if (Array.isArray(value.public_top10) && value.public_top10.length > 0) {
        html += renderAssetTable('Top 10 Public Assets', value.public_top10);
      }
      if (value.note) {
        html += `<div style=\"margin-top:8px; color:#555; font-size:0.97em;\">${value.note}</div>`;
      }
      card.innerHTML += html;
      reportContent.appendChild(card);
      return;
    }

    // Special rendering for builds
    if (key === 'builds' && typeof value === 'object' && value !== null) {
      card.innerHTML += renderBuildsTable(value);
      reportContent.appendChild(card);
      return;
    }

    if (typeof value === 'object' && value !== null) {
      const table = document.createElement('table');
      table.className = 'metrics-table';
      const tbody = document.createElement('tbody');
      Object.entries(value).forEach(([k, v]) => {
        const row = document.createElement('tr');
        const cellK = document.createElement('td');
        cellK.textContent = FRIENDLY_NAMES[k] || k;
        const cellV = document.createElement('td');
        cellV.textContent = v;
        row.appendChild(cellK);
        row.appendChild(cellV);
        tbody.appendChild(row);
      });
      table.appendChild(tbody);
      card.appendChild(table);
    } else {
      const val = document.createElement('div');
      val.textContent = value;
      card.appendChild(val);
    }
    reportContent.appendChild(card);
  });
}


function formatReportDate(filename) {
  // Expecting format: dev-report-YYYYMMDD-HHMMSS.json
  const match = filename.match(/(\d{4})(\d{2})(\d{2})-(\d{2})(\d{2})(\d{2})/);
  if (!match) return filename;
  const [_, year, month, day] = match;
  const date = new Date(`${year}-${month}-${day}`);
  const options = { day: '2-digit', month: 'short', year: 'numeric' };
  return 'Report - ' + date.toLocaleDateString(undefined, options);
}

async function loadDashboard() {
  const reports = await fetchReportList();
  // Populate dropdown
  reportSelect.innerHTML = '';
  reports.forEach((filename, idx) => {
    const opt = document.createElement('option');
    opt.value = filename;
    opt.textContent = formatReportDate(filename);
    if (idx === reports.length - 1) opt.selected = true; // Latest by default
    reportSelect.appendChild(opt);
  });
  // Load latest by default
  loadReport(reportSelect.value);
}

async function loadReport(filename) {
  try {
    const report = await fetchReport(filename);
    renderReport(report, filename);
  } catch (e) {
    reportContent.innerHTML = '<div style="color:red">' + e.message + '</div>';
  }
}

reportSelect.addEventListener('change', e => {
  loadReport(e.target.value);
});

window.addEventListener('DOMContentLoaded', loadDashboard);
