// Enhanced Dashboard for Bayaan Project Metrics
class MetricsDashboard {
  constructor() {
    this.reportsDir = '../reports/';
    this.reportSelect = document.getElementById('reportSelect');
    this.refreshBtn = document.getElementById('refreshBtn');
    this.loadingOverlay = document.getElementById('loadingOverlay');
    this.charts = {};
    this.currentReport = null;

    this.init();
  }

  async init() {
    this.setupEventListeners();
    await this.loadDashboard();
  }

  setupEventListeners() {
    this.reportSelect.addEventListener('change', (e) => {
      this.loadReport(e.target.value);
    });

    this.refreshBtn.addEventListener('click', () => {
      this.loadDashboard();
    });
  }

  showLoading() {
    this.loadingOverlay.classList.remove('hidden');
  }

  hideLoading() {
    this.loadingOverlay.classList.add('hidden');
  }

  async fetchReportList() {
    // Try to parse directory listing (works with most web servers)
    try {
      const response = await fetch(this.reportsDir);
      if (response.ok) {
        const html = await response.text();

        // Parse HTML to extract JSON file links
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const links = doc.querySelectorAll('a[href$=".json"]');

        const reports = Array.from(links)
          .map(link => link.getAttribute('href'))
          .filter(href => href && href.includes('dev-report-'))
          .map(href => href.split('/').pop()) // Get just the filename
          .sort((a, b) => {
            // Sort by date/time in filename (newest first)
            const dateA = this.extractDateFromFilename(a);
            const dateB = this.extractDateFromFilename(b);
            return dateB - dateA;
          });

        if (reports.length > 0) {
          console.log('Reports loaded via directory listing:', reports);
          return reports;
        }
      }
    } catch (error) {
      console.warn('Directory listing not available:', error.message);
    }

    // Fallback: Use hardcoded list
    console.warn('Using fallback hardcoded report list');
    return [
      'dev-report-20250907-213307.json',
      'dev-report-20250906-234657.json',
      'dev-report-20250901-052741.json',
    ];
  }

  extractDateFromFilename(filename) {
    // Extract date from filename like: dev-report-20250907-213307.json
    const match = filename.match(/(\d{4})(\d{2})(\d{2})-(\d{2})(\d{2})(\d{2})/);
    if (match) {
      const [_, year, month, day, hour, minute, second] = match;
      // Create date object with local timezone interpretation
      return new Date(
        parseInt(year),
        parseInt(month) - 1, // Month is 0-indexed
        parseInt(day),
        parseInt(hour),
        parseInt(minute),
        parseInt(second)
      );
    }
    return new Date(0); // Fallback for invalid dates
  }

  async fetchReport(filename) {
    const res = await fetch(this.reportsDir + filename);
    if (!res.ok) throw new Error('Failed to load report: ' + filename);
    return res.json();
  }


  formatReportDate(filename) {
    const match = filename.match(/(\d{4})(\d{2})(\d{2})-(\d{2})(\d{2})(\d{2})/);
    if (!match) return filename;
    const [_, year, month, day, hour, minute, second] = match;

    // Create date object with local timezone interpretation
    const date = new Date(
      parseInt(year),
      parseInt(month) - 1, // Month is 0-indexed
      parseInt(day),
      parseInt(hour),
      parseInt(minute),
      parseInt(second)
    );

    // Format date and time separately to ensure local timezone
    const dateStr = date.toLocaleDateString(undefined, {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });

    const timeStr = date.toLocaleTimeString(undefined, {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });

    return `${dateStr}, ${timeStr}`;
  }

  formatBytes(bytes) {
    if (!bytes || bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatBuildTime(seconds) {
    if (!seconds || seconds === 0) return '0s';
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return mins > 0 ? `${mins}m ${secs}s` : `${secs}s`;
  }

  getAssetSizeClass(bytes) {
    if (bytes > 10 * 1024 * 1024) return 'large'; // > 10MB
    if (bytes > 1 * 1024 * 1024) return 'medium'; // > 1MB
    return 'small';
  }

  async loadDashboard() {
    this.showLoading();
    try {
      const reports = await this.fetchReportList();
      this.populateReportSelect(reports);

      // Load the latest report by default
      if (reports.length > 0) {
        await this.loadReport(reports[0]);
      }
    } catch (error) {
      console.error('Failed to load dashboard:', error);
      this.showError('Failed to load dashboard: ' + error.message);
    } finally {
      this.hideLoading();
    }
  }

  populateReportSelect(reports) {
    this.reportSelect.innerHTML = '';
    reports.forEach((filename, idx) => {
      const option = document.createElement('option');
      option.value = filename;
      option.textContent = this.formatReportDate(filename);
      if (idx === 0) option.selected = true;
      this.reportSelect.appendChild(option);
    });
  }

  async loadReport(filename) {
    this.showLoading();
    try {
      this.currentReport = await this.fetchReport(filename);
      this.renderDashboard();
    } catch (error) {
      console.error('Failed to load report:', error);
      this.showError('Failed to load report: ' + error.message);
    } finally {
      this.hideLoading();
    }
  }

  showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
    document.querySelector('.dashboard-main').innerHTML = '';
    document.querySelector('.dashboard-main').appendChild(errorDiv);
  }

  renderDashboard() {
    if (!this.currentReport) return;

    this.renderProjectInfo();
    this.renderOverviewCards();
    this.renderCharts();
    this.renderDetailedMetrics();
  }

  renderOverviewCards() {
    const report = this.currentReport;
    const overviewCards = document.getElementById('overviewCards');

    const cards = [
      {
        title: 'Total Files',
        value: report.file_count?.total_files?.toLocaleString() || '0',
        subtitle: 'Project files',
        icon: 'fas fa-file-code',
        color: '#667eea'
      },
      {
        title: 'Lines of Code',
        value: report.lines_of_code?.total_loc?.toLocaleString() || '0',
        subtitle: 'Total LOC',
        icon: 'fas fa-code',
        color: '#764ba2'
      },
      {
        title: 'Build Status',
        value: this.getBuildCompletionCount(report.builds),
        subtitle: 'Completed builds',
        icon: 'fas fa-cogs',
        color: '#10b981'
      }
    ];

    overviewCards.innerHTML = cards.map(card => `
      <div class="overview-card fade-in">
        <div class="overview-card-header">
          <span class="overview-card-title">${card.title}</span>
          <div class="overview-card-icon" style="background: ${card.color}">
            <i class="${card.icon}"></i>
          </div>
        </div>
        <div class="overview-card-value">${card.value}</div>
        <div class="overview-card-subtitle">${card.subtitle}</div>
      </div>
    `).join('');
  }

  getBuildCompletionCount(builds) {
    if (!builds) return '0/8';
    let completed = 0;
    let total = 8; // 2 apps × 2 platforms × 2 build types

    ['private_app', 'public_app'].forEach(app => {
      if (builds[app]) {
        ['android', 'ios'].forEach(platform => {
          if (builds[app][platform]) {
            ['debug', 'release'].forEach(type => {
              if (builds[app][platform][type] && builds[app][platform][type].build_time_seconds > 0) {
                completed++;
              }
            });
          }
        });
      }
    });

    return `${completed}/${total}`;
  }

  getQualityScore(dartAnalysis) {
    if (!dartAnalysis) return 'N/A';
    const { errors = 0, warnings = 0, info = 0 } = dartAnalysis;
    const total = errors + warnings + info;
    if (total === 0) return '100%';

    // Simple scoring: errors are weighted more heavily
    const score = Math.max(0, 100 - (errors * 10 + warnings * 2 + info * 0.5));
    return Math.round(score) + '%';
  }

  renderCharts() {
    this.renderLanguageChart();
    this.renderFileCountChart();
    this.renderLOCChart();
    this.renderBuildChart();
  }

  renderLanguageChart() {
    const ctx = document.getElementById('languageChart').getContext('2d');
    const data = this.currentReport.lines_of_code?.by_filetype || {};

    if (this.charts.languageChart) {
      this.charts.languageChart.destroy();
    }

    const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'];

    this.charts.languageChart = new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: Object.keys(data),
        datasets: [{
          data: Object.values(data),
          backgroundColor: colors.slice(0, Object.keys(data).length),
          borderWidth: 2,
          borderColor: '#fff'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true
            }
          },
          tooltip: {
            callbacks: {
              label: (context) => {
                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                const percentage = ((context.parsed / total) * 100).toFixed(1);
                return `${context.label}: ${context.parsed.toLocaleString()} lines (${percentage}%)`;
              }
            }
          }
        }
      }
    });
  }

  renderFileCountChart() {
    const ctx = document.getElementById('fileCountChart').getContext('2d');
    const data = this.currentReport.file_count?.by_filetype || {};

    if (this.charts.fileCountChart) {
      this.charts.fileCountChart.destroy();
    }

    const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'];

    this.charts.fileCountChart = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: Object.keys(data),
        datasets: [{
          label: 'File Count',
          data: Object.values(data),
          backgroundColor: colors.slice(0, Object.keys(data).length),
          borderRadius: 4,
          borderSkipped: false
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: (context) => `${context.label}: ${context.parsed.y} files`
            }
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              precision: 0
            }
          }
        }
      }
    });
  }

  renderLOCChart() {
    const ctx = document.getElementById('locChart').getContext('2d');
    const report = this.currentReport;

    if (this.charts.locChart) {
      this.charts.locChart.destroy();
    }

    const data = {
      labels: ['Private App', 'Public App', 'Packages', 'Android Libs'],
      datasets: [{
        label: 'Lines of Code',
        data: [
          report.lines_of_code?.private_loc || 0,
          report.lines_of_code?.public_loc || 0,
          report.lines_of_code?.packages_loc || 0,
          report.lines_of_code?.android_libs_loc || 0
        ],
        backgroundColor: ['#667eea', '#764ba2', '#f093fb', '#f5576c'],
        borderRadius: 4,
        borderSkipped: false
      }]
    };

    this.charts.locChart = new Chart(ctx, {
      type: 'bar',
      data: data,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: (context) => `${context.label}: ${context.parsed.y.toLocaleString()} lines`
            }
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: function(value) {
                return value.toLocaleString();
              }
            }
          }
        }
      }
    });
  }

  renderBuildChart() {
    const ctx = document.getElementById('buildChart').getContext('2d');
    const builds = this.currentReport.builds || {};

    if (this.charts.buildChart) {
      this.charts.buildChart.destroy();
    }

    const buildData = [];
    const labels = [];
    const colors = [];
    const colorMap = {
      'Private Android': '#667eea',
      'Private iOS': '#764ba2',
      'Public Android': '#f093fb',
      'Public iOS': '#f5576c'
    };

    ['private_app', 'public_app'].forEach(app => {
      if (builds[app]) {
        ['android', 'ios'].forEach(platform => {
          if (builds[app][platform] && builds[app][platform].release) {
            const data = builds[app][platform].release;
            if (data.build_successful) {
              const label = `${app === 'private_app' ? 'Private' : 'Public'} ${platform === 'android' ? 'Android' : 'iOS'}`;
              labels.push(label);
              buildData.push(data.build_time_seconds);
              colors.push(colorMap[label]);
            }
          }
        });
      }
    });

    this.charts.buildChart = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: labels,
        datasets: [{
          label: 'Build Time (seconds)',
          data: buildData,
          backgroundColor: colors,
          borderRadius: 4,
          borderSkipped: false
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: (context) => `${context.label}: ${this.formatBuildTime(context.parsed.y)}`
            }
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: (value) => this.formatBuildTime(value)
            }
          }
        }
      }
    });
  }


  renderDetailedMetrics() {
    this.renderCodeQuality();
    this.renderAssetAnalysis();
    this.renderBuildResults();
  }

  renderCodeQuality() {
    const container = document.getElementById('codeQuality');
    const analysis = this.currentReport.dart_analysis || {};

    container.innerHTML = `
      <div class="quality-metric errors">
        <span class="quality-metric-label">Errors</span>
        <span class="quality-metric-value">${analysis.errors || 0}</span>
      </div>
      <div class="quality-metric warnings">
        <span class="quality-metric-label">Warnings</span>
        <span class="quality-metric-value">${analysis.warnings || 0}</span>
      </div>
      <div class="quality-metric info">
        <span class="quality-metric-label">Info</span>
        <span class="quality-metric-value">${analysis.info || 0}</span>
      </div>
    `;
  }

  renderAssetAnalysis() {
    const container = document.getElementById('assetAnalysis');
    const assets = this.currentReport.assets || {};

    // Combine and get top 10 assets from both private and public
    const allAssets = [];
    if (assets.private && assets.private.top_assets) {
      assets.private.top_assets.forEach(asset => {
        allAssets.push({ ...asset, source: 'Private' });
      });
    }
    if (assets.public && assets.public.top_assets) {
      assets.public.top_assets.forEach(asset => {
        allAssets.push({ ...asset, source: 'Public' });
      });
    }

    // Sort by size and get top 10
    allAssets.sort((a, b) => b.size - a.size);
    const top10Assets = allAssets.slice(0, 10);

    // Combine asset types from both private and public
    const combinedTypes = new Map();

    if (assets.private && assets.private.types) {
      assets.private.types.forEach(type => {
        const key = type.ext;
        if (combinedTypes.has(key)) {
          const existing = combinedTypes.get(key);
          combinedTypes.set(key, {
            ext: key,
            count: existing.count + type.count,
            size: existing.size + type.size
          });
        } else {
          combinedTypes.set(key, { ...type });
        }
      });
    }

    if (assets.public && assets.public.types) {
      assets.public.types.forEach(type => {
        const key = type.ext;
        if (combinedTypes.has(key)) {
          const existing = combinedTypes.get(key);
          combinedTypes.set(key, {
            ext: key,
            count: existing.count + type.count,
            size: existing.size + type.size
          });
        } else {
          combinedTypes.set(key, { ...type });
        }
      });
    }

    const sortedTypes = Array.from(combinedTypes.values()).sort((a, b) => b.size - a.size);

    let html = '<div class="asset-analysis-grid">';

    // Top 10 Assets (combined)
    if (top10Assets.length > 0) {
      html += `
        <div class="asset-table-container">
          <h4>Top 10 Assets by Size</h4>
          <table class="metrics-table">
            <thead><tr><th>Asset</th><th>Source</th><th>Size</th></tr></thead>
            <tbody>`;
      top10Assets.forEach(asset => {
        const sizeClass = this.getAssetSizeClass(asset.size);
        const truncatedName = asset.name.length > 30 ? asset.name.substring(0, 27) + '...' : asset.name;
        html += `<tr>
          <td title="${asset.name}">${truncatedName}</td>
          <td><span class="source-badge ${asset.source.toLowerCase()}">${asset.source}</span></td>
          <td><span class="asset-size ${sizeClass}">${this.formatBytes(asset.size)}</span></td>
        </tr>`;
      });
      html += `
            </tbody>
          </table>
        </div>`;
    }

    // Asset types summary (combined)
    if (sortedTypes.length > 0) {
      html += `
        <div class="asset-table-container">
          <h4>Asset Types (Combined)</h4>
          <table class="metrics-table">
            <thead><tr><th>Type</th><th>Count</th><th>Total Size</th></tr></thead>
            <tbody>`;
      sortedTypes.forEach(type => {
        html += `<tr>
          <td>.${type.ext}</td>
          <td>${type.count}</td>
          <td><span class="asset-size">${this.formatBytes(type.size)}</span></td>
        </tr>`;
      });
      html += `
            </tbody>
          </table>
        </div>`;
    }

    html += '</div>';
    container.innerHTML = html || '<p>No asset data available</p>';
  }

  renderBuildResults() {
    const container = document.getElementById('buildResults');
    const builds = this.currentReport.builds || {};

    let html = '<div class="build-results-grid">';

    ['private_app', 'public_app'].forEach(app => {
      if (builds[app]) {
        ['android', 'ios'].forEach(platform => {
          if (builds[app][platform]) {
            ['debug', 'release'].forEach(type => {
              const data = builds[app][platform][type];
              const appName = app === 'private_app' ? 'Private' : 'Public';
              const platformName = platform.charAt(0).toUpperCase() + platform.slice(1);

              let status, statusClass, statusIcon;
              if (!data || data.build_time_seconds === 0) {
                status = 'Not Built';
                statusClass = 'not-built';
                statusIcon = 'fas fa-minus-circle';
              } else if (data.build_successful) {
                status = 'Success';
                statusClass = 'success';
                statusIcon = 'fas fa-check-circle';
              } else {
                status = 'Failed';
                statusClass = 'failed';
                statusIcon = 'fas fa-times-circle';
              }

              html += `
                <div class="build-result-item ${statusClass}">
                  <div class="build-result-header">
                    <div class="build-result-title">${appName} ${platformName}</div>
                    <span class="status-indicator status-${statusClass}">
                      <i class="${statusIcon}"></i> ${status}
                    </span>
                  </div>
                  <div class="build-result-metrics">
                    <div class="build-result-metric">
                      <div class="build-result-metric-label">Type</div>
                      <div class="build-result-metric-value">
                        <span class="build-chip ${type}">${type}</span>
                      </div>
                    </div>
                    <div class="build-result-metric">
                      <div class="build-result-metric-label">Time</div>
                      <div class="build-result-metric-value">${data ? this.formatBuildTime(data.build_time_seconds) : 'N/A'}</div>
                    </div>
                    <div class="build-result-metric">
                      <div class="build-result-metric-label">Size</div>
                      <div class="build-result-metric-value">${data ? this.formatBytes(data.output_size_bytes) : 'N/A'}</div>
                    </div>
                  </div>
                </div>
              `;
            });
          }
        });
      }
    });

    html += '</div>';
    container.innerHTML = html;
  }

  renderProjectInfo() {
    const container = document.getElementById('projectInfo');
    const metadata = this.currentReport.metadata || {};

    container.innerHTML = `
      <table class="metrics-table">
        <tbody>
          <tr><td>Project Name</td><td>${metadata.project_name || 'N/A'}</td></tr>
          <tr><td>Report Version</td><td>${metadata.report_version || 'N/A'}</td></tr>
          <tr><td>Generated</td><td>${metadata.timestamp ? new Date(metadata.timestamp).toLocaleString() : 'N/A'}</td></tr>
          <tr><td>Collection Duration</td><td>${metadata.collection_duration ? new Date(metadata.collection_duration).toLocaleString() : 'N/A'}</td></tr>
        </tbody>
      </table>
    `;
  }
}

// Initialize the dashboard when the page loads
window.addEventListener('DOMContentLoaded', () => {
  new MetricsDashboard();
});
