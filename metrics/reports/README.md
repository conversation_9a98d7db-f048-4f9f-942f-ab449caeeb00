# Dynamic Report Discovery

The dashboard now supports dynamic discovery of report files instead of using a hardcoded list. This means new reports will automatically appear in the dashboard without needing to update the code.

## How It Works

The dashboard tries multiple methods to discover reports, in order of preference:

### Method 1: PHP API (Recommended)
If you're running a web server with PHP support:

```bash
# The dashboard will automatically use list.php
# No additional setup required
```

### Method 2: Node.js API
If you prefer Node.js, you can run the API server:

```bash
cd /path/to/metrics/reports
node list.js
```

Then update the dashboard to use: `http://localhost:3001/list`

### Method 3: Python API
If you prefer Python, you can run the API server:

```bash
cd /path/to/metrics/reports
python3 list.py
```

Then update the dashboard to use: `http://localhost:3002/list`

### Method 4: Directory Listing
If your web server supports directory listing, the dashboard will parse the HTML to find JSON files.

### Method 5: Pattern Discovery
The dashboard will attempt to discover reports by trying common filename patterns from recent dates.

### Method 6: Fallback
If all else fails, it uses a hardcoded list of known reports.

## File Naming Convention

Reports should follow this naming pattern:
```
dev-report-YYYYMMDD-HHMMSS.json
```

Examples:
- `dev-report-20250907-213307.json`
- `dev-report-20250906-234657.json`

## Server Setup

### Apache with PHP
```apache
# .htaccess (if needed)
Options +Indexes
DirectoryIndex index.html list.php
```

### Nginx with PHP
```nginx
location /metrics/reports/ {
    try_files $uri $uri/ /metrics/reports/list.php;
    autoindex on;
}
```

### Static File Server
For development, you can use any static file server:

```bash
# Python
python3 -m http.server 8000

# Node.js (with serve)
npx serve .

# PHP built-in server
php -S localhost:8000
```

## Testing

You can test the API endpoints directly:

```bash
# PHP API
curl http://localhost:8000/metrics/reports/list.php

# Node.js API
curl http://localhost:3001/list

# Python API
curl http://localhost:3002/list
```

All should return a JSON array of report filenames, sorted newest first:

```json
[
  "dev-report-20250907-213307.json",
  "dev-report-20250906-234657.json",
  "dev-report-20250901-052741.json"
]
```

## Troubleshooting

1. **No reports found**: Check that report files follow the naming convention
2. **CORS errors**: Ensure your server sends proper CORS headers
3. **API not working**: Check server logs and ensure the API script has proper permissions
4. **Fallback used**: Check browser console for error messages about why auto-discovery failed

The dashboard will log which method it successfully used in the browser console.
