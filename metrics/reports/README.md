# Dynamic Report Discovery

The dashboard now supports dynamic discovery of report files instead of using a hardcoded list. This means new reports will automatically appear in the dashboard without needing to update the code.

## How It Works

The dashboard uses a simple and reliable method to discover reports:

### Method 1: Directory Listing (Primary)
The dashboard fetches the reports directory and parses the HTML to find JSON files. This works with most web servers that have directory listing enabled.

### Method 2: Fallback
If directory listing fails, it uses a hardcoded list of known reports.

## File Naming Convention

Reports should follow this naming pattern:
```
dev-report-YYYYMMDD-HHMMSS.json
```

Examples:
- `dev-report-20250907-213307.json`
- `dev-report-20250906-234657.json`

## Server Setup

### Apache
```apache
# .htaccess (if needed)
Options +Indexes
DirectoryIndex index.html
```

### Nginx
```nginx
location /metrics/reports/ {
    autoindex on;
}
```

### Static File Server
For development, you can use any static file server with directory listing:

```bash
# Python
python3 -m http.server 8000

# Node.js (with serve)
npx serve .

# PHP built-in server
php -S localhost:8000
```

## Testing

You can test directory listing by visiting the reports folder in your browser:
```
http://localhost:8000/metrics/reports/
```

You should see a list of files including the JSON reports.

## Troubleshooting

1. **No reports found**: Check that report files follow the naming convention
2. **Directory listing not working**: Ensure your web server has directory listing enabled
3. **Fallback used**: Check browser console for error messages about why auto-discovery failed

The dashboard will log which method it successfully used in the browser console.
