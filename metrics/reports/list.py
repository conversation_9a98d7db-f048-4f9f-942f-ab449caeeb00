#!/usr/bin/env python3

import os
import json
import glob
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse

class ReportsHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        parsed_path = urlparse(self.path)
        
        # Enable CORS
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        
        if parsed_path.path in ['/', '/list']:
            try:
                # Get all dev-report JSON files
                pattern = os.path.join(os.path.dirname(__file__), 'dev-report-*.json')
                files = glob.glob(pattern)
                
                # Extract just the filenames and sort (newest first)
                reports = [os.path.basename(f) for f in files]
                reports.sort(reverse=True)
                
                response = json.dumps(reports)
                self.wfile.write(response.encode())
            except Exception as e:
                error_response = json.dumps({'error': str(e)})
                self.wfile.write(error_response.encode())
        else:
            error_response = json.dumps({'error': 'Not found'})
            self.wfile.write(error_response.encode())

def run_server(port=3002):
    server_address = ('', port)
    httpd = HTTPServer(server_address, ReportsHandler)
    print(f"Reports API server running on http://localhost:{port}")
    print("Available endpoints:")
    print(f"  GET http://localhost:{port}/list - List all reports")
    httpd.serve_forever()

if __name__ == '__main__':
    run_server()
