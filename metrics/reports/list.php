<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Get all JSON files in the current directory
$files = glob('*.json');

// Filter for dev-report files and sort by date (newest first)
$reports = array_filter($files, function($file) {
    return strpos($file, 'dev-report-') === 0;
});

// Sort by filename (which contains date/time) in descending order
usort($reports, function($a, $b) {
    return strcmp($b, $a);
});

// Return the list as JSON
echo json_encode(array_values($reports));
?>
