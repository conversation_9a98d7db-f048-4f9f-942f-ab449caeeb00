#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Simple HTTP server to list reports
const http = require('http');
const url = require('url');

const PORT = 3001;

const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  res.setHeader('Content-Type', 'application/json');
  
  if (parsedUrl.pathname === '/list' || parsedUrl.pathname === '/') {
    try {
      // Read all files in the current directory
      const files = fs.readdirSync(__dirname);
      
      // Filter for dev-report JSON files
      const reports = files
        .filter(file => file.startsWith('dev-report-') && file.endsWith('.json'))
        .sort((a, b) => b.localeCompare(a)); // Sort newest first
      
      res.writeHead(200);
      res.end(JSON.stringify(reports));
    } catch (error) {
      res.writeHead(500);
      res.end(JSON.stringify({ error: 'Failed to read directory' }));
    }
  } else {
    res.writeHead(404);
    res.end(JSON.stringify({ error: 'Not found' }));
  }
});

if (require.main === module) {
  server.listen(PORT, () => {
    console.log(`Reports API server running on http://localhost:${PORT}`);
    console.log('Available endpoints:');
    console.log(`  GET http://localhost:${PORT}/list - List all reports`);
  });
}

module.exports = server;
